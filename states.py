# states.py (New File)

from rlbot.agents.base_agent import Simple<PERSON>ontrollerState
from rlbot.utils.structures.game_data_struct import GameTickPacket

# Import the utils we need
from util.drive import steer_toward_target, should_powerslide
from util.vec import Vec3
from util.ball_prediction_analysis import find_best_intercept_point, find_shot_opportunity
from util.orientation import Orientation, relative_location
from util.game_awareness import OpponentTracker, BallTrajectoryAnalyzer, GameSituationAnalyzer
from util.ball_physics import BallContactPhysics, ShotCalculator, BallManipulation
from util.advanced_rendering import AdvancedRenderer


class State:
    """
    The base class for all states. Your bot's main class will hold an instance
    of one of these, and call its execute method each tick.
    """
    def __init__(self, bot):
        self.bot = bot # A reference to the main bot class

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        raise NotImplementedError


class Attack(State):
    def __init__(self, bot):
        super().__init__(bot)
        self.opponent_tracker = OpponentTracker()
        self.advanced_renderer = AdvancedRenderer(bot.renderer)
        self.ball_plan = None

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)
        ball_velocity = Vec3(packet.game_ball.physics.velocity)
        car_orientation = Orientation(my_car.physics.rotation)

        # Update opponent tracking
        self.opponent_tracker.update(packet, self.bot.team)

        # Determine enemy goal location
        enemy_goal_y = 5120 if self.bot.team == 0 else -5120
        enemy_goal_location = Vec3(0, enemy_goal_y, 0)

        # Check if we should be patient instead of attacking
        ball_prediction = self.bot.get_ball_prediction_struct()
        if GameSituationAnalyzer.should_be_patient(car_location, ball_location, ball_prediction, self.opponent_tracker):
            # Determine reason for patience
            if BallTrajectoryAnalyzer.is_ball_coming_down(ball_prediction, car_location, 800):
                return PatientPositioning(self.bot, "ball_coming_down").execute(packet)
            elif self.opponent_tracker.will_opponent_reach_ball_first(ball_location, car_location, car_velocity):
                return PatientPositioning(self.bot, "opponent_closer").execute(packet)

        # Check for aerial opportunities
        aerial_target = GameSituationAnalyzer.should_attempt_aerial(
            car_location, ball_location, my_car.boost, ball_prediction)
        if aerial_target and my_car.boost > 40:
            # Calculate time to aerial target
            if ball_prediction and ball_prediction.num_slices > 0:
                for i in range(60, min(180, ball_prediction.num_slices), 10):
                    ball_slice = ball_prediction.slices[i]
                    future_ball = Vec3(ball_slice.physics.location)
                    if future_ball.dist(aerial_target) < 100:  # Found matching slice
                        return EnhancedAerial(self.bot, aerial_target, ball_slice.game_seconds).execute(packet)

        # SOPHISTICATED BALL PHYSICS ANALYSIS
        distance_to_ball = car_location.dist(ball_location)

        # Determine attack strategy based on situation
        attack_strategy = self._determine_attack_strategy(
            car_location, ball_location, enemy_goal_location, distance_to_ball, my_car.boost)

        # Create sophisticated ball manipulation plan
        self.ball_plan = BallManipulation.plan_ball_touch(
            ball_location, ball_velocity, car_location, car_velocity,
            attack_strategy, enemy_goal_location)

        # Calculate shot analysis if shooting
        shot_target = None
        shot_quality = 0
        if attack_strategy == "shot":
            shot_target, shot_quality = ShotCalculator.find_best_shot_angle(
                ball_location, enemy_goal_location)
            self.ball_plan["shot_target"] = shot_target
            self.ball_plan["shot_quality"] = shot_quality

        # Get target position from ball plan
        target_location = self.ball_plan["approach_position"]
        should_boost = self.ball_plan["should_boost"]
        should_flip = self.ball_plan["should_flip"]

        # SOPHISTICATED CONTROL GENERATION
        controls = SimpleControllerState()

        # Calculate steering toward approach position
        controls.steer = steer_toward_target(my_car, target_location)
        controls.throttle = 1.0

        # Advanced boost logic based on ball plan
        controls.boost = should_boost and my_car.boost > 0

        # Flip logic for powerful hits
        if should_flip and distance_to_ball < 250:
            controls.jump = True
            # Calculate flip direction based on target
            target_direction = self.ball_plan["target_direction"]
            car_to_target = relative_location(car_location, car_orientation,
                                            ball_location + target_direction * 100)

            # Front flip toward target
            if car_to_target.x > 0:
                controls.pitch = -1.0
            # Side flip if target is to the side
            elif abs(car_to_target.y) > abs(car_to_target.x):
                controls.roll = 1.0 if car_to_target.y > 0 else -1.0

        # Advanced powerslide for precise positioning
        if should_powerslide(my_car, target_location, 500):
            controls.handbrake = True

        # Fine throttle control when close to ball
        if distance_to_ball < 300:
            hit_strength = self.ball_plan["hit_strength"]
            controls.throttle = max(0.3, hit_strength)  # Reduce speed for precise hits

        # COMPREHENSIVE VISUAL FEEDBACK
        self._render_sophisticated_feedback(packet, attack_strategy, shot_target, shot_quality)

        return controls

    def _determine_attack_strategy(self, car_location: Vec3, ball_location: Vec3,
                                 goal_location: Vec3, distance_to_ball: float, boost: int) -> str:
        """Determine the best attack strategy based on current situation"""

        # Distance to goal from ball
        ball_to_goal_distance = ball_location.dist(goal_location)

        # If ball is close to goal and we're close to ball, shoot
        if ball_to_goal_distance < 2000 and distance_to_ball < 400:
            return "shot"

        # If ball is far from goal, try to dribble or control
        elif ball_to_goal_distance > 3000 and distance_to_ball < 200:
            return "dribble"

        # If ball is moving fast toward our goal, clear it
        elif ball_to_goal_distance < 1500:
            return "clear"

        # Default to shot if we have good positioning
        elif distance_to_ball < 600:
            return "shot"

        # Otherwise, control the ball
        return "control"

    def _render_sophisticated_feedback(self, packet: GameTickPacket, strategy: str,
                                     shot_target: Vec3, shot_quality: float):
        """Render comprehensive visual feedback for attack state"""

        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Render ball physics visualization
        if self.ball_plan:
            self.advanced_renderer.render_ball_physics_visualization(ball_location, self.ball_plan)

        # Render shot analysis if shooting
        if strategy == "shot" and shot_target:
            enemy_goal_y = 5120 if self.bot.team == 0 else -5120
            enemy_goal_location = Vec3(0, enemy_goal_y, 0)
            self.advanced_renderer.render_shot_analysis(
                ball_location, enemy_goal_location, shot_target, shot_quality)

        # Render game state HUD
        game_info = {
            "current_state": f"Attack ({strategy})",
            "boost": my_car.boost,
            "speed": Vec3(my_car.physics.velocity).length(),
            "decision_reason": f"Strategy: {strategy.upper()}",
            "ball_distance": car_location.dist(ball_location)
        }
        self.advanced_renderer.render_game_state_hud(car_location, game_info)

        # Render ball prediction with physics
        ball_prediction = self.bot.get_ball_prediction_struct()
        if ball_prediction:
            self.advanced_renderer.render_ball_prediction_advanced(ball_prediction, ball_location)
        

class Defend(State):
    def __init__(self, bot):
        super().__init__(bot)
        self.opponent_tracker = OpponentTracker()

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        # Update opponent tracking for better defensive decisions
        self.opponent_tracker.update(packet, self.bot.team)
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)
        ball_velocity = Vec3(packet.game_ball.physics.velocity)

        # Goal and field information
        our_goal_y = -5120 if self.bot.team == 0 else 5120
        our_goal_location = Vec3(0, our_goal_y, 0)

        # Determine defensive strategy
        defense_mode = "Shadow"
        target_location = car_location
        should_boost = False

        # Check if ball is coming toward our goal
        ball_prediction = self.bot.get_ball_prediction_struct()
        is_dangerous_shot = False

        if ball_prediction is not None and ball_prediction.num_slices > 0:
            # Check if ball will be near our goal in the next few seconds
            for i in range(0, min(180, ball_prediction.num_slices), 10):  # Check 3 seconds ahead
                future_ball = Vec3(ball_prediction.slices[i].physics.location)
                distance_to_our_goal = future_ball.dist(our_goal_location)

                if distance_to_our_goal < 2000:  # Ball will be near our goal
                    is_dangerous_shot = True
                    break

        # Calculate ball's approach angle to our goal
        ball_to_goal = our_goal_location - ball_location
        ball_goal_distance = ball_to_goal.length()

        if is_dangerous_shot or ball_goal_distance < 3000:
            # Emergency defense - get between ball and goal
            defense_mode = "Block"

            # Position between ball and goal, closer to goal
            if ball_to_goal.length() > 0:
                ball_to_goal_normalized = ball_to_goal.normalized()
                target_location = our_goal_location + ball_to_goal_normalized * 800
            else:
                target_location = our_goal_location

            # Adjust for goal width - stay within goal posts
            if abs(target_location.x) > 700:
                target_location.x = 700 if target_location.x > 0 else -700

            should_boost = ball_goal_distance < 1500  # Emergency boost

        elif ball_goal_distance < 4000:
            # Shadow defense - stay between ball and goal but further out
            defense_mode = "Shadow"

            # Calculate shadow position
            if ball_to_goal.length() > 0:
                ball_to_goal_normalized = ball_to_goal.normalized()
                shadow_distance = max(1200, ball_goal_distance * 0.4)
                target_location = our_goal_location + ball_to_goal_normalized * shadow_distance

                # Adjust based on ball velocity
                if ball_velocity.length() > 500:
                    # Ball is moving, predict where it's going
                    ball_future = ball_location + ball_velocity.normalized() * 500
                    ball_future_to_goal = our_goal_location - ball_future
                    if ball_future_to_goal.length() > 0:
                        target_location = our_goal_location + ball_future_to_goal.normalized() * shadow_distance
            else:
                target_location = our_goal_location

        else:
            # Back post rotation
            defense_mode = "BackPost"

            # Go to back post (far post relative to ball)
            back_post_x = 700 if ball_location.x < 0 else -700
            target_location = Vec3(back_post_x, our_goal_y + (200 if self.bot.team == 0 else -200), 0)

        # Smart boost usage for defense
        distance_to_target = car_location.dist(target_location)
        if defense_mode == "Block" and distance_to_target > 500:
            should_boost = my_car.boost > 15
        elif car_velocity.length() < 800 and distance_to_target > 1000:
            should_boost = my_car.boost > 25

        # Draw debug information
        color = self.bot.renderer.green()
        if defense_mode == "Block":
            color = self.bot.renderer.red()
        elif defense_mode == "Shadow":
            color = self.bot.renderer.orange()
        elif defense_mode == "BackPost":
            color = self.bot.renderer.blue()

        self.bot.renderer.draw_line_3d(car_location, target_location, color)
        self.bot.renderer.draw_string_3d(car_location, 1, 1, f"Defend: {defense_mode}", color)

        # Generate controls
        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target_location)
        controls.throttle = 1.0
        controls.boost = should_boost and my_car.boost > 0

        # Use powerslide for quick defensive adjustments
        if abs(controls.steer) > 0.9 and car_velocity.length() > 600:
            controls.handbrake = True

        return controls


class CollectBoost(State):
    def __init__(self, bot):
        super().__init__(bot)

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Smart boost collection - consider game situation
        our_goal_y = -5120 if self.bot.team == 0 else 5120
        our_goal_location = Vec3(0, our_goal_y, 0)
        ball_to_goal_distance = ball_location.dist(our_goal_location)

        # If ball is very close to our goal, abandon boost collection
        if ball_to_goal_distance < 1500:
            return Defend(self.bot).execute(packet)

        # Get available boost pads
        active_full_pads = [pad for pad in self.bot.boost_pad_tracker.get_full_boosts() if pad.is_active]
        active_small_pads = [pad for pad in self.bot.boost_pad_tracker.get_small_boosts() if pad.is_active]

        target_pad = None
        boost_strategy = "None"

        # Strategy 1: Go for big boost if we're very low and it's safe
        if my_car.boost < 15 and active_full_pads:
            # Find safest big boost (furthest from opponents)
            safest_pad = None
            best_safety_score = -1

            for pad in active_full_pads:
                safety_score = 0
                pad_distance = car_location.dist(pad.location)

                # Prefer closer pads
                safety_score += max(0, 2000 - pad_distance) / 2000

                # Avoid pads near opponents
                for i, opponent in enumerate(packet.game_cars):
                    if i != self.bot.index and opponent.team != self.bot.team:
                        opponent_location = Vec3(opponent.physics.location)
                        opponent_distance = opponent_location.dist(pad.location)
                        if opponent_distance < 1000:
                            safety_score -= 0.5

                # Prefer pads on our side of the field
                if (pad.location.y < 0 and self.bot.team == 0) or (pad.location.y > 0 and self.bot.team == 1):
                    safety_score += 0.3

                if safety_score > best_safety_score:
                    best_safety_score = safety_score
                    safest_pad = pad

            if safest_pad:
                target_pad = safest_pad
                boost_strategy = "Big Boost"

        # Strategy 2: Collect small pads if big boost isn't safe/available
        elif my_car.boost < 40 and active_small_pads:
            # Find small pads along our path
            closest_small_pads = sorted(active_small_pads,
                                      key=lambda pad: car_location.dist(pad.location))[:3]

            # Choose small pad that's roughly in the direction we want to go
            ball_direction_vec = ball_location - car_location
            if ball_direction_vec.length() > 0:
                ball_direction = ball_direction_vec.normalized()
                best_alignment = -1

                for pad in closest_small_pads:
                    pad_direction_vec = pad.location - car_location
                    if pad_direction_vec.length() > 0:
                        pad_direction = pad_direction_vec.normalized()
                        alignment = ball_direction.dot(pad_direction)
                    else:
                        alignment = 0

                    if alignment > best_alignment and car_location.dist(pad.location) < 800:
                        best_alignment = alignment
                        target_pad = pad
                        boost_strategy = "Small Boost"

        # If no good boost option, return to attacking
        if not target_pad:
            return Attack(self.bot).execute(packet)

        target_location = target_pad.location
        distance_to_pad = car_location.dist(target_location)

        # Generate controls
        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target_location)
        controls.throttle = 1.0

        # Smart boost usage while collecting boost
        if boost_strategy == "Big Boost":
            # Use boost to get to big boost faster if we're far
            controls.boost = distance_to_pad > 1000 and my_car.boost > 5
        else:
            # Don't waste boost getting small boost
            controls.boost = False

        # Use powerslide for sharp turns to boost pads
        if abs(controls.steer) > 0.7 and car_velocity.length() > 600:
            controls.handbrake = True

        # Debug rendering
        color = self.bot.renderer.yellow() if boost_strategy == "Big Boost" else self.bot.renderer.orange()
        self.bot.renderer.draw_line_3d(car_location, target_location, color)
        self.bot.renderer.draw_string_3d(car_location, 1, 1, f"Boost: {boost_strategy}", color)

        return controls


class Aerial(State):
    def __init__(self, bot, target_location: Vec3, target_time: float):
        super().__init__(bot)
        self.target_location = target_location
        self.target_time = target_time
        self.started_aerial = False
        self.takeoff_time = 0

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        car_orientation = Orientation(my_car.physics.rotation)

        current_time = packet.game_info.seconds_elapsed

        # Check if we should start the aerial
        if not self.started_aerial:
            # Only start aerial if we have enough boost and ball is high enough
            if my_car.boost < 30 or self.target_location.z < 200:
                # Not enough boost or target too low, fall back to ground play
                return Attack(self.bot).execute(packet)

            self.started_aerial = True
            self.takeoff_time = current_time

        # Calculate time remaining and distance to target
        time_remaining = self.target_time - current_time
        distance_to_target = car_location.dist(self.target_location)

        # If we're too late or target is too far, give up aerial
        if time_remaining < 0.2 or distance_to_target > 2000:
            return Attack(self.bot).execute(packet)

        # Calculate desired velocity to reach target
        if time_remaining > 0:
            desired_velocity = (self.target_location - car_location) / time_remaining
        else:
            direction_to_target = self.target_location - car_location
            if direction_to_target.length() > 0:
                desired_velocity = direction_to_target.normalized() * 1000
            else:
                desired_velocity = Vec3(0, 0, 0)

        # Current velocity relative to desired
        velocity_error = desired_velocity - car_velocity

        # Convert to car's local coordinates for control
        local_velocity_error = Vec3(
            velocity_error.dot(car_orientation.forward),
            velocity_error.dot(car_orientation.right),
            velocity_error.dot(car_orientation.up)
        )

        controls = SimpleControllerState()

        # Aerial control - use pitch, yaw, roll to orient toward target
        controls.pitch = self._limit_control(-local_velocity_error.x / 500.0)
        controls.yaw = self._limit_control(local_velocity_error.y / 500.0)
        controls.roll = self._limit_control(-local_velocity_error.y / 300.0)

        # Boost control - boost if we need more speed toward target
        forward_speed_needed = local_velocity_error.x
        controls.boost = forward_speed_needed > 100 and my_car.boost > 0

        # Jump control - double jump for initial takeoff
        time_since_takeoff = current_time - self.takeoff_time
        if time_since_takeoff < 0.2:
            controls.jump = True
        elif 0.2 <= time_since_takeoff < 0.4 and not my_car.double_jumped:
            controls.jump = True

        # Throttle - use for fine speed control
        controls.throttle = 1.0 if forward_speed_needed > 0 else 0.0

        # Debug rendering
        self.bot.renderer.draw_line_3d(car_location, self.target_location, self.bot.renderer.purple())
        self.bot.renderer.draw_string_3d(car_location, 1, 1, "State: Aerial", self.bot.renderer.purple())

        return controls

    def _limit_control(self, value: float) -> float:
        """Limit control values to [-1, 1] range"""
        return max(-1.0, min(1.0, value))


class Kickoff(State):
    def __init__(self, bot):
        super().__init__(bot)
        self.kickoff_started = False
        self.kickoff_type = None
        self.start_time = 0

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        ball_location = Vec3(packet.game_ball.physics.location)

        current_time = packet.game_info.seconds_elapsed

        # Initialize kickoff if not started
        if not self.kickoff_started:
            self.kickoff_started = True
            self.start_time = current_time
            self.kickoff_type = self._determine_kickoff_type(car_location)

        # Check if kickoff is over (ball has moved significantly)
        if ball_location.length() > 200:  # Ball moved from center
            return Attack(self.bot).execute(packet)

        # Execute kickoff strategy based on position
        if self.kickoff_type == "center":
            return self._center_kickoff(packet)
        elif self.kickoff_type == "diagonal":
            return self._diagonal_kickoff(packet)
        elif self.kickoff_type == "back":
            return self._back_kickoff(packet)
        else:
            # Default to center kickoff
            return self._center_kickoff(packet)

    def _determine_kickoff_type(self, car_location: Vec3) -> str:
        """Determine kickoff type based on starting position"""
        x, y = abs(car_location.x), abs(car_location.y)

        # Center kickoff positions
        if x < 500 and y > 3000:
            return "center"
        # Diagonal kickoff positions
        elif x > 1500 and y > 2000:
            return "diagonal"
        # Back positions (should go for boost or cheat)
        elif y > 4000:
            return "back"
        else:
            return "center"  # Default

    def _center_kickoff(self, packet: GameTickPacket) -> SimpleControllerState:
        """Fast kickoff from center position"""
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Target slightly to the side of ball for better hit
        offset_x = 50 if car_location.x > 0 else -50
        target = Vec3(offset_x, 0, 0)

        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target)
        controls.throttle = 1.0

        # Boost until close to ball
        distance_to_ball = car_location.dist(ball_location)
        controls.boost = distance_to_ball > 500 and my_car.boost > 0

        # Jump when very close for a flip
        if distance_to_ball < 300 and car_velocity.length() > 1000:
            controls.jump = True
            # Add front flip for power
            car_orientation = Orientation(my_car.physics.rotation)
            relative_ball = relative_location(car_location, car_orientation, ball_location)
            if relative_ball.x > 0:  # Ball is in front
                controls.pitch = -1.0

        self.bot.renderer.draw_string_3d(car_location, 1, 1, "Kickoff: Center", self.bot.renderer.cyan())
        return controls

    def _diagonal_kickoff(self, packet: GameTickPacket) -> SimpleControllerState:
        """Diagonal kickoff with curve approach"""
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Curve toward ball center
        target = Vec3(0, 0, 0)
        distance_to_ball = car_location.dist(ball_location)

        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target)
        controls.throttle = 1.0
        controls.boost = distance_to_ball > 800 and my_car.boost > 0

        # Jump and flip when close
        if distance_to_ball < 400 and car_velocity.length() > 1200:
            controls.jump = True
            controls.pitch = -1.0  # Front flip

        self.bot.renderer.draw_string_3d(car_location, 1, 1, "Kickoff: Diagonal", self.bot.renderer.cyan())
        return controls

    def _back_kickoff(self, packet: GameTickPacket) -> SimpleControllerState:
        """Back position - go for boost or cheat up"""
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)

        # If we have low boost, go for corner boost
        if my_car.boost < 50:
            # Go to corner boost
            corner_x = 3584 if car_location.x > 0 else -3584
            corner_y = -4240 if self.bot.team == 0 else 4240
            target = Vec3(corner_x, corner_y, 0)

            controls = SimpleControllerState()
            controls.steer = steer_toward_target(my_car, target)
            controls.throttle = 1.0
            controls.boost = my_car.boost > 0

            self.bot.renderer.draw_string_3d(car_location, 1, 1, "Kickoff: Get Boost", self.bot.renderer.yellow())
            return controls
        else:
            # Cheat up for follow-up play
            target = Vec3(0, 0, 0)  # Move toward center

            controls = SimpleControllerState()
            controls.steer = steer_toward_target(my_car, target)
            controls.throttle = 1.0
            controls.boost = car_location.dist(target) > 2000

            self.bot.renderer.draw_string_3d(car_location, 1, 1, "Kickoff: Cheat", self.bot.renderer.orange())
            return controls


class Save(State):
    def __init__(self, bot, ball_slice=None):
        super().__init__(bot)
        self.target_ball_slice = ball_slice
        self.save_type = "ground"

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Goal information
        our_goal_y = -5120 if self.bot.team == 0 else 5120
        our_goal_location = Vec3(0, our_goal_y, 0)

        # Determine save target
        target_location = ball_location
        should_aerial = False

        # Use predicted ball position if available
        if self.target_ball_slice:
            target_location = Vec3(self.target_ball_slice.physics.location)

            # Check if we need an aerial save
            if target_location.z > 200 and my_car.boost > 20:
                should_aerial = True
                self.save_type = "aerial"

        # If ball is very close to goal line, position for goal line save
        ball_to_goal_distance = ball_location.dist(our_goal_location)
        if ball_to_goal_distance < 800:
            self.save_type = "goal_line"
            # Position directly between ball and goal center
            ball_to_goal = our_goal_location - ball_location
            if ball_to_goal.length() > 0:
                target_location = ball_location + ball_to_goal.normalized() * 100
            else:
                target_location = ball_location

            # Clamp to goal area
            target_location.x = max(-700, min(700, target_location.x))
            target_location.y = our_goal_y + (100 if self.bot.team == 0 else -100)

        controls = SimpleControllerState()

        if should_aerial and self.save_type == "aerial":
            # Aerial save
            time_to_ball = 1.0  # Estimate
            if self.target_ball_slice:
                time_to_ball = self.target_ball_slice.game_seconds - packet.game_info.seconds_elapsed

            # Use aerial logic
            aerial_state = Aerial(self.bot, target_location, packet.game_info.seconds_elapsed + time_to_ball)
            return aerial_state.execute(packet)

        else:
            # Ground save
            controls.steer = steer_toward_target(my_car, target_location)
            controls.throttle = 1.0

            # Boost if we need to get there quickly
            distance_to_target = car_location.dist(target_location)
            urgency = max(0, 1.0 - (ball_to_goal_distance / 2000.0))  # More urgent as ball gets closer

            controls.boost = (distance_to_target > 300 and urgency > 0.5 and my_car.boost > 10)

            # Jump for saves when ball is slightly elevated
            if (target_location.z > 100 and target_location.z < 300 and
                distance_to_target < 200 and car_velocity.length() > 500):
                controls.jump = True

            # Powerslide for quick adjustments
            if abs(controls.steer) > 0.8 and car_velocity.length() > 400:
                controls.handbrake = True

        # Debug rendering
        color = self.bot.renderer.red()
        if self.save_type == "aerial":
            color = self.bot.renderer.purple()
        elif self.save_type == "goal_line":
            color = self.bot.renderer.orange()

        self.bot.renderer.draw_line_3d(car_location, target_location, color)
        self.bot.renderer.draw_string_3d(car_location, 1, 1, f"Save: {self.save_type}", color)

        return controls


class PatientPositioning(State):
    """State for smart waiting and positioning when bot cannot reach ball first"""

    def __init__(self, bot, reason="waiting"):
        super().__init__(bot)
        self.reason = reason
        self.target_position = None

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Goal information
        our_goal_y = -5120 if self.bot.team == 0 else 5120
        our_goal_location = Vec3(0, our_goal_y, 0)

        ball_prediction = self.bot.get_ball_prediction_struct()

        # Determine positioning strategy based on reason
        if self.reason == "ball_coming_down":
            # Position for ball landing
            landing_point = BallTrajectoryAnalyzer.find_ball_landing_point(ball_prediction, 3.0)
            if landing_point:
                self.target_position = landing_point
                positioning_mode = "Landing"
            else:
                # Fallback to shadow defense
                self.target_position = GameSituationAnalyzer.calculate_defensive_position(
                    car_location, ball_location, our_goal_location, 1200)
                positioning_mode = "Shadow"

        elif self.reason == "opponent_closer":
            # Shadow defense positioning
            self.target_position = GameSituationAnalyzer.calculate_defensive_position(
                car_location, ball_location, our_goal_location, 1500)
            positioning_mode = "Shadow"

        else:
            # Default: position between ball and goal
            ball_to_goal = our_goal_location - ball_location
            if ball_to_goal.length() > 0:
                direction = ball_to_goal.normalized()
                self.target_position = our_goal_location + direction * 1000
            else:
                self.target_position = our_goal_location
            positioning_mode = "Defensive"

        # Calculate movement to target
        distance_to_target = car_location.dist(self.target_position)

        controls = SimpleControllerState()

        # Smart movement - slow down when close to target
        if distance_to_target < 200:
            # We're close, just maintain position
            controls.throttle = 0.1
            controls.steer = steer_toward_target(my_car, self.target_position)
        elif distance_to_target < 500:
            # Approach slowly
            controls.throttle = 0.5
            controls.steer = steer_toward_target(my_car, self.target_position)
        else:
            # Move to position normally
            controls.throttle = 1.0
            controls.steer = steer_toward_target(my_car, self.target_position)

            # Use boost if we're far and need to get there quickly
            if distance_to_target > 1000 and my_car.boost > 20:
                controls.boost = True

        # Face the ball while positioning
        ball_direction = ball_location - car_location
        if ball_direction.length() > 0:
            # Adjust steering slightly toward ball for better orientation
            ball_steer = steer_toward_target(my_car, ball_location)
            controls.steer = (controls.steer * 0.7) + (ball_steer * 0.3)

        # Use powerslide for quick adjustments
        if abs(controls.steer) > 0.6 and car_velocity.length() > 400:
            controls.handbrake = True

        # Enhanced visual feedback
        color = self.bot.renderer.cyan()
        if positioning_mode == "Landing":
            color = self.bot.renderer.green()
        elif positioning_mode == "Shadow":
            color = self.bot.renderer.orange()

        self.bot.renderer.draw_line_3d(car_location, self.target_position, color)
        self.bot.renderer.draw_string_3d(car_location, 1, 1, f"Patient: {positioning_mode}", color)

        # Draw ball landing prediction if available
        if self.reason == "ball_coming_down" and ball_prediction:
            landing_point = BallTrajectoryAnalyzer.find_ball_landing_point(ball_prediction, 3.0)
            if landing_point:
                self.bot.renderer.draw_rect_3d(landing_point, 8, 8, True, self.bot.renderer.green(), centered=True)

        return controls


class EnhancedAerial(State):
    """Enhanced aerial state with better takeoff and control"""

    def __init__(self, bot, target_location: Vec3, target_time: float):
        super().__init__(bot)
        self.target_location = target_location
        self.target_time = target_time
        self.aerial_phase = "takeoff"  # takeoff, flight, abort
        self.takeoff_time = 0
        self.jump_count = 0

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        car_orientation = Orientation(my_car.physics.rotation)

        current_time = packet.game_info.seconds_elapsed
        time_remaining = self.target_time - current_time

        controls = SimpleControllerState()

        # Abort conditions
        if (time_remaining < 0.2 or
            my_car.boost < 5 or
            car_location.dist(self.target_location) > 2500):
            self.aerial_phase = "abort"
            return Attack(self.bot).execute(packet)  # Fall back to ground play

        if self.aerial_phase == "takeoff":
            if my_car.has_wheel_contact:
                # Initial takeoff phase
                if self.takeoff_time == 0:
                    self.takeoff_time = current_time

                # Point toward target before jumping
                controls.steer = steer_toward_target(my_car, self.target_location)
                controls.throttle = 1.0

                # Jump timing
                time_since_takeoff = current_time - self.takeoff_time
                if time_since_takeoff < 0.1:
                    controls.jump = True
                    self.jump_count = 1
                elif 0.1 <= time_since_takeoff < 0.25 and self.jump_count == 1:
                    controls.jump = True
                    self.jump_count = 2
                    # Pitch up for aerial
                    controls.pitch = -0.8
                else:
                    # Start boosting and transition to flight
                    controls.boost = True
                    controls.pitch = -0.5  # Pitch up
                    if time_since_takeoff > 0.3:
                        self.aerial_phase = "flight"
            else:
                # We're airborne, transition to flight
                self.aerial_phase = "flight"

        elif self.aerial_phase == "flight":
            # Advanced aerial control
            target_direction = self.target_location - car_location

            if target_direction.length() > 0:
                target_direction = target_direction.normalized()

                # Convert to car's local coordinates
                local_target = Vec3(
                    target_direction.dot(car_orientation.forward),
                    target_direction.dot(car_orientation.right),
                    target_direction.dot(car_orientation.up)
                )

                # PID-like control for orientation
                controls.pitch = self._limit_control(-local_target.z * 2.0)
                controls.yaw = self._limit_control(local_target.y * 1.5)
                controls.roll = self._limit_control(-local_target.y * 0.8)

                # Boost control - boost toward target
                if local_target.x > 0.3:  # Target is in front
                    controls.boost = my_car.boost > 0

                # Throttle for fine control
                controls.throttle = 1.0 if local_target.x > 0 else 0.0

        # Enhanced visual feedback
        self.bot.renderer.draw_line_3d(car_location, self.target_location, self.bot.renderer.purple())
        self.bot.renderer.draw_string_3d(car_location, 1, 1, f"Aerial: {self.aerial_phase}", self.bot.renderer.purple())

        # Draw target sphere
        self.bot.renderer.draw_rect_3d(self.target_location, 12, 12, True, self.bot.renderer.purple(), centered=True)

        return controls

    def _limit_control(self, value: float) -> float:
        """Limit control values to [-1, 1] range"""
        return max(-1.0, min(1.0, value))