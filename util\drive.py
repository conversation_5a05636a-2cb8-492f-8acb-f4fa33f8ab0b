import math

from rlbot.utils.structures.game_data_struct import PlayerInfo

from util.orientation import Orientation, relative_location
from util.vec import Vec3


def limit_to_safe_range(value: float) -> float:
    """
    Controls like throttle, steer, pitch, yaw, and roll need to be in the range of -1 to 1.
    This will ensure your number is in that range. Something like 0.45 will stay as it is,
    but a value of -5.6 would be changed to -1.
    """
    if value < -1:
        return -1
    if value > 1:
        return 1
    return value


def steer_toward_target(car: PlayerInfo, target: Vec3) -> float:
    relative = relative_location(Vec3(car.physics.location), Orientation(car.physics.rotation), target)
    angle = math.atan2(relative.y, relative.x)
    return limit_to_safe_range(angle * 5)


def should_powerslide(car: PlayerInfo, target: Vec3, min_speed: float = 500) -> bool:
    """
    Determines if the car should powerslide based on the turn angle and speed.
    Returns True if a powerslide would be beneficial.
    """
    car_velocity = Vec3(car.physics.velocity)
    if car_velocity.length() < min_speed:
        return False

    relative = relative_location(Vec3(car.physics.location), Orientation(car.physics.rotation), target)
    angle = abs(math.atan2(relative.y, relative.x))

    # Powerslide for sharp turns (more than 45 degrees)
    return angle > math.pi / 4


def calculate_turn_radius(speed: float) -> float:
    """
    Calculate the approximate turn radius for a given speed.
    Used for path planning and turn optimization.
    """
    if speed <= 0:
        return 0
    # Empirical formula based on Rocket League physics
    return speed * speed / 1000.0


def predict_car_position(car: PlayerInfo, time_ahead: float) -> Vec3:
    """
    Predict where the car will be after a given time, assuming constant velocity.
    """
    car_location = Vec3(car.physics.location)
    car_velocity = Vec3(car.physics.velocity)
    return car_location + car_velocity * time_ahead


def calculate_intercept_steering(car: PlayerInfo, target: Vec3, target_velocity: Vec3,
                               intercept_time: float) -> float:
    """
    Calculate steering to intercept a moving target.
    """
    # Predict where target will be
    predicted_target = target + target_velocity * intercept_time
    return steer_toward_target(car, predicted_target)
