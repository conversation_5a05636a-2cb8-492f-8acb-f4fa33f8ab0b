# Advanced Rendering System for Comprehensive Visual Feedback

from typing import List, Dict, Any
from util.vec import Vec3


class AdvancedRenderer:
    """Sophisticated rendering system for detailed bot visualization"""
    
    def __init__(self, renderer):
        self.renderer = renderer
        self.render_queue = []
        self.persistent_elements = {}
        
    def clear_frame(self):
        """Clear temporary render elements for new frame"""
        self.render_queue.clear()
    
    def add_persistent_element(self, key: str, element: dict):
        """Add element that persists across frames"""
        self.persistent_elements[key] = element
    
    def remove_persistent_element(self, key: str):
        """Remove persistent element"""
        if key in self.persistent_elements:
            del self.persistent_elements[key]
    
    def render_ball_physics_visualization(self, ball_location: Vec3, ball_plan: dict):
        """Render detailed ball physics visualization"""
        
        # Render approach position
        approach_pos = ball_plan.get("approach_position", ball_location)
        self.renderer.draw_rect_3d(approach_pos, 8, 8, True, 
                                 self.renderer.cyan(), centered=True)
        self.renderer.draw_string_3d(approach_pos + Vec3(0, 0, 50), 1, 1,
                                   "APPROACH", self.renderer.cyan())
        
        # Render contact point
        contact_point = ball_plan.get("contact_point", ball_location)
        self.renderer.draw_rect_3d(contact_point, 6, 6, True,
                                 self.renderer.red(), centered=True)
        self.renderer.draw_string_3d(contact_point + Vec3(0, 0, 30), 1, 1,
                                   "CONTACT", self.renderer.red())
        
        # Render target direction
        target_dir = ball_plan.get("target_direction", Vec3(1, 0, 0))
        target_end = ball_location + (target_dir * 300)
        self.renderer.draw_line_3d(ball_location, target_end, self.renderer.yellow())
        
        # Draw arrow head for direction
        arrow_size = 20
        arrow_left = target_end + Vec3(-arrow_size, -arrow_size, 0)
        arrow_right = target_end + Vec3(-arrow_size, arrow_size, 0)
        self.renderer.draw_line_3d(target_end, arrow_left, self.renderer.yellow())
        self.renderer.draw_line_3d(target_end, arrow_right, self.renderer.yellow())
        
        # Render strategy info
        strategy = ball_plan.get("strategy", "unknown")
        hit_strength = ball_plan.get("hit_strength", 0.5)
        should_boost = ball_plan.get("should_boost", False)
        should_flip = ball_plan.get("should_flip", False)
        
        info_text = f"Strategy: {strategy.upper()}"
        self.renderer.draw_string_3d(ball_location + Vec3(0, 0, 150), 1.5, 1.5,
                                   info_text, self.renderer.white())
        
        power_text = f"Power: {hit_strength:.1f}"
        power_color = self.renderer.red() if hit_strength > 0.7 else \
                     self.renderer.yellow() if hit_strength > 0.4 else self.renderer.green()
        self.renderer.draw_string_3d(ball_location + Vec3(0, 0, 120), 1, 1,
                                   power_text, power_color)
        
        if should_boost:
            self.renderer.draw_string_3d(ball_location + Vec3(0, 0, 90), 1, 1,
                                       "BOOST!", self.renderer.orange())
        
        if should_flip:
            self.renderer.draw_string_3d(ball_location + Vec3(0, 0, 60), 1, 1,
                                       "FLIP!", self.renderer.purple())
    
    def render_shot_analysis(self, ball_location: Vec3, goal_location: Vec3, 
                           shot_target: Vec3, shot_quality: float):
        """Render shot analysis visualization"""
        
        # Draw shot line
        shot_color = self.renderer.green() if shot_quality > 0.7 else \
                    self.renderer.yellow() if shot_quality > 0.4 else self.renderer.red()
        
        self.renderer.draw_line_3d(ball_location, shot_target, shot_color)
        
        # Draw goal outline
        goal_width = 1400
        goal_height = 640
        goal_left = goal_location + Vec3(-goal_width/2, 0, 0)
        goal_right = goal_location + Vec3(goal_width/2, 0, 0)
        goal_top_left = goal_left + Vec3(0, 0, goal_height)
        goal_top_right = goal_right + Vec3(0, 0, goal_height)
        
        # Goal frame
        self.renderer.draw_line_3d(goal_left, goal_right, self.renderer.white())
        self.renderer.draw_line_3d(goal_left, goal_top_left, self.renderer.white())
        self.renderer.draw_line_3d(goal_right, goal_top_right, self.renderer.white())
        self.renderer.draw_line_3d(goal_top_left, goal_top_right, self.renderer.white())
        
        # Shot target indicator
        self.renderer.draw_rect_3d(shot_target, 12, 12, True, shot_color, centered=True)
        
        # Shot quality indicator
        quality_text = f"Shot Quality: {shot_quality:.1f}"
        self.renderer.draw_string_3d(ball_location + Vec3(100, 0, 100), 1, 1,
                                   quality_text, shot_color)
    
    def render_game_state_hud(self, car_location: Vec3, game_info: dict):
        """Render comprehensive game state HUD"""
        
        # Main HUD position (above car)
        hud_base = car_location + Vec3(0, 0, 200)
        
        # Current state
        state_name = game_info.get("current_state", "Unknown")
        state_color = self._get_state_color(state_name)
        self.renderer.draw_string_3d(hud_base, 2, 2, f"STATE: {state_name}", state_color)
        
        # Boost level with bar
        boost_level = game_info.get("boost", 0)
        boost_text = f"Boost: {boost_level}"
        boost_color = self.renderer.green() if boost_level > 50 else \
                     self.renderer.yellow() if boost_level > 20 else self.renderer.red()
        self.renderer.draw_string_3d(hud_base + Vec3(0, 0, -30), 1.5, 1.5,
                                   boost_text, boost_color)
        
        # Boost bar visualization
        bar_width = 100
        bar_height = 8
        bar_fill = (boost_level / 100.0) * bar_width
        bar_start = hud_base + Vec3(-bar_width/2, 0, -50)
        bar_end = bar_start + Vec3(bar_fill, 0, 0)
        
        # Draw boost bar background
        self.renderer.draw_line_3d(bar_start, bar_start + Vec3(bar_width, 0, 0), 
                                 self.renderer.white())
        # Draw boost bar fill
        if bar_fill > 0:
            self.renderer.draw_line_3d(bar_start, bar_end, boost_color)
        
        # Speed indicator
        speed = game_info.get("speed", 0)
        speed_text = f"Speed: {speed:.0f}"
        speed_color = self.renderer.red() if speed > 1800 else \
                     self.renderer.yellow() if speed > 1000 else self.renderer.white()
        self.renderer.draw_string_3d(hud_base + Vec3(0, 0, -70), 1, 1,
                                   speed_text, speed_color)
        
        # Decision reasoning
        decision_reason = game_info.get("decision_reason", "")
        if decision_reason:
            self.renderer.draw_string_3d(hud_base + Vec3(0, 0, -100), 1, 1,
                                       f"Reason: {decision_reason}", self.renderer.cyan())
        
        # Ball distance
        ball_distance = game_info.get("ball_distance", 0)
        distance_text = f"Ball Dist: {ball_distance:.0f}"
        self.renderer.draw_string_3d(hud_base + Vec3(0, 0, -130), 1, 1,
                                   distance_text, self.renderer.white())
    
    def render_opponent_analysis(self, opponents: List[dict]):
        """Render opponent tracking and analysis"""
        
        for i, opponent in enumerate(opponents):
            opp_location = opponent.get("location", Vec3(0, 0, 0))
            opp_distance = opponent.get("distance_to_ball", 0)
            opp_threat_level = opponent.get("threat_level", "low")
            
            # Opponent marker
            threat_color = self.renderer.red() if opp_threat_level == "high" else \
                          self.renderer.yellow() if opp_threat_level == "medium" else \
                          self.renderer.white()
            
            self.renderer.draw_rect_3d(opp_location + Vec3(0, 0, 100), 10, 10, True,
                                     threat_color, centered=True)
            
            # Opponent info
            opp_text = f"OPP{i+1}: {opp_distance:.0f}u"
            self.renderer.draw_string_3d(opp_location + Vec3(0, 0, 120), 1, 1,
                                       opp_text, threat_color)
            
            # Threat level indicator
            threat_text = f"Threat: {opp_threat_level.upper()}"
            self.renderer.draw_string_3d(opp_location + Vec3(0, 0, 90), 0.8, 0.8,
                                       threat_text, threat_color)
    
    def render_ball_prediction_advanced(self, ball_prediction, current_ball_location: Vec3):
        """Render advanced ball prediction with physics analysis"""
        
        if not ball_prediction or ball_prediction.num_slices == 0:
            return
        
        prev_location = current_ball_location
        bounce_count = 0
        
        for i in range(0, min(300, ball_prediction.num_slices), 10):  # 5 seconds
            ball_slice = ball_prediction.slices[i]
            current_location = Vec3(ball_slice.physics.location)
            current_velocity = Vec3(ball_slice.physics.velocity)
            
            # Color code by physics state
            if current_location.z < 100:
                # Ground level
                color = self.renderer.white()
                if i > 0:
                    prev_slice = ball_prediction.slices[i-10] if i >= 10 else ball_prediction.slices[0]
                    prev_z = prev_slice.physics.location.z
                    if prev_z > 150 and current_location.z < 100:
                        # Bounce detected
                        bounce_count += 1
                        self.renderer.draw_rect_3d(current_location, 8, 8, True,
                                                 self.renderer.green(), centered=True)
                        self.renderer.draw_string_3d(current_location + Vec3(0, 0, 30), 0.8, 0.8,
                                                   f"BOUNCE {bounce_count}", self.renderer.green())
            elif current_location.z < 300:
                # Low aerial
                color = self.renderer.yellow()
            else:
                # High aerial
                color = self.renderer.cyan()
            
            # Draw trajectory line
            self.renderer.draw_line_3d(prev_location, current_location, color)
            
            # Mark significant points
            if i % 60 == 0:  # Every second
                time_marker = i // 60
                self.renderer.draw_string_3d(current_location + Vec3(0, 0, 20), 0.6, 0.6,
                                           f"{time_marker}s", self.renderer.white())
            
            prev_location = current_location
    
    def _get_state_color(self, state_name: str):
        """Get color for different states"""
        state_colors = {
            "Attack": self.renderer.red(),
            "Defend": self.renderer.green(),
            "CollectBoost": self.renderer.yellow(),
            "Aerial": self.renderer.purple(),
            "EnhancedAerial": self.renderer.purple(),
            "PatientPositioning": self.renderer.cyan(),
            "Save": self.renderer.orange(),
            "Kickoff": self.renderer.blue()
        }
        return state_colors.get(state_name, self.renderer.white())
    
    def render_all_elements(self):
        """Render all queued and persistent elements"""
        # Render persistent elements
        for element in self.persistent_elements.values():
            self._render_element(element)
        
        # Render frame elements
        for element in self.render_queue:
            self._render_element(element)
    
    def _render_element(self, element: dict):
        """Render individual element based on type"""
        element_type = element.get("type", "unknown")
        
        if element_type == "line":
            self.renderer.draw_line_3d(element["start"], element["end"], element["color"])
        elif element_type == "string":
            self.renderer.draw_string_3d(element["position"], element["scale_x"], 
                                       element["scale_y"], element["text"], element["color"])
        elif element_type == "rect":
            self.renderer.draw_rect_3d(element["position"], element["width"], 
                                     element["height"], element["filled"], 
                                     element["color"], centered=element.get("centered", False))
