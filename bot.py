# bot.py (Corrected Version)

from rlbot.agents.base_agent import BaseAgent, SimpleControllerState
from rlbot.utils.structures.game_data_struct import GameTickPacket

from util.boost_pad_tracker import BoostPadTracker
from util.vec import Vec3

# We import our new state classes
# Make sure you have created the states.py file!
from states import Attack, Defend, CollectBoost, Aerial, Kickoff, Save, EnhancedAerial
from util.game_awareness import OpponentTracker

class MyBot(BaseAgent):

    def __init__(self, name, team, index):
        super().__init__(name, team, index)
        self.boost_pad_tracker = BoostPadTracker()
        self.opponent_tracker = OpponentTracker()
        self.state = None  # This will hold our current state object, e.g., Attack()

    def initialize_agent(self):
        # Set up information about the boost pads now that the game is active and the info is available
        self.boost_pad_tracker.initialize_boosts(self.get_field_info())
        # The bot starts in an attack state.
        self.state = Attack(self)

    def get_output(self, packet: GameTickPacket) -> SimpleControllerState:
        """
        This function will be called by the framework many times per second. This is where you can
        see the motion of the ball, etc. and return controls to drive your car.
        """
        # Keep our boost pad info updated with which pads are currently active
        self.boost_pad_tracker.update_boost_status(packet)

        # Update opponent tracking
        self.opponent_tracker.update(packet, self.team)

        # --- THIS IS THE "BRAIN" ---
        # It decides which state we should be in and assigns it to self.state
        new_state = self.choose_state(packet)
        if type(new_state) is not type(self.state):
            self.state = new_state
        # ---------------------------------

        # Execute the logic of our current state
        controls = self.state.execute(packet)

        # Enhanced visual feedback - show comprehensive bot status
        self._render_bot_status(packet)

        return controls

    def _render_bot_status(self, packet: GameTickPacket):
        """Render comprehensive visual feedback about bot's decision making"""
        my_car = packet.game_cars[self.index]
        car_location = Vec3(my_car.physics.location)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Show current state prominently
        state_name = type(self.state).__name__
        self.renderer.draw_string_3d(car_location + Vec3(0, 0, 100), 2, 2,
                                   f"STATE: {state_name}", self.renderer.white())

        # Show boost level with color coding
        boost_color = self.renderer.green() if my_car.boost > 50 else \
                     self.renderer.yellow() if my_car.boost > 20 else self.renderer.red()
        self.renderer.draw_string_3d(car_location + Vec3(0, 0, 80), 1, 1,
                                   f"Boost: {my_car.boost}", boost_color)

        # Show ball prediction trajectory
        ball_prediction = self.get_ball_prediction_struct()
        if ball_prediction and ball_prediction.num_slices > 0:
            # Draw ball trajectory for next 3 seconds
            prev_location = ball_location
            for i in range(0, min(180, ball_prediction.num_slices), 15):
                ball_slice = ball_prediction.slices[i]
                current_location = Vec3(ball_slice.physics.location)

                # Color code by height
                if current_location.z < 100:
                    color = self.renderer.white()  # Ground level
                elif current_location.z < 300:
                    color = self.renderer.yellow()  # Low aerial
                else:
                    color = self.renderer.cyan()  # High aerial

                self.renderer.draw_line_3d(prev_location, current_location, color)
                prev_location = current_location

        # Show opponent positions
        for i in range(packet.num_cars):
            if i != self.index and packet.game_cars[i].team != self.team:
                opponent_location = Vec3(packet.game_cars[i].physics.location)
                self.renderer.draw_string_3d(opponent_location + Vec3(0, 0, 50), 1, 1,
                                           "OPPONENT", self.renderer.red())

        # Show game awareness info
        from util.game_awareness import BallTrajectoryAnalyzer
        if ball_prediction:
            # Show ball landing prediction
            landing_point = BallTrajectoryAnalyzer.find_ball_landing_point(ball_prediction, 5.0)
            if landing_point:
                self.renderer.draw_rect_3d(landing_point, 10, 10, True,
                                         self.renderer.green(), centered=True)
                self.renderer.draw_string_3d(landing_point + Vec3(0, 0, 30), 1, 1,
                                           "LANDING", self.renderer.green())

    # Enhanced decision making for platinum level play
    def choose_state(self, packet: GameTickPacket):
        my_car = packet.game_cars[self.index]
        car_location = Vec3(my_car.physics.location)
        car_velocity = Vec3(my_car.physics.velocity)
        ball_location = Vec3(packet.game_ball.physics.location)
        ball_velocity = Vec3(packet.game_ball.physics.velocity)

        # Priority 0: Kickoff detection
        # Check if this is a kickoff situation (ball at center, cars at spawn)
        if (ball_location.length() < 100 and  # Ball is at center
            packet.game_info.is_round_active and  # Game is active
            packet.game_info.seconds_elapsed < 5):  # Early in the game/round

            if not isinstance(self.state, Kickoff):
                self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Kickoff!", self.renderer.cyan())
                return Kickoff(self)

        # Get ball prediction for advanced decision making
        ball_prediction = self.get_ball_prediction_struct()

        # Priority 1: Enhanced Aerial opportunities with better decision making
        if (ball_prediction is not None and my_car.boost > 40 and
            not isinstance(self.state, (Aerial, EnhancedAerial))):

            from util.game_awareness import GameSituationAnalyzer
            aerial_target = GameSituationAnalyzer.should_attempt_aerial(
                car_location, ball_location, my_car.boost, ball_prediction)

            if aerial_target:
                # Find the corresponding time for this aerial target
                for i in range(60, min(180, ball_prediction.num_slices), 10):
                    ball_slice = ball_prediction.slices[i]
                    future_ball = Vec3(ball_slice.physics.location)

                    if future_ball.dist(aerial_target) < 100:  # Found matching slice
                        self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Enhanced Aerial!", self.renderer.purple())
                        return EnhancedAerial(self, aerial_target, ball_slice.game_seconds)

        # Priority 2: Emergency saves (ball very close to our goal)
        our_goal_y = -5120 if self.team == 0 else 5120
        our_goal_location = Vec3(0, our_goal_y, 0)
        ball_to_goal_distance = ball_location.dist(our_goal_location)

        # Check for immediate save situations
        if ball_to_goal_distance < 1200:
            # Look for incoming shots in ball prediction
            save_slice = None
            if ball_prediction is not None:
                for i in range(0, min(60, ball_prediction.num_slices), 5):  # Next 1 second
                    future_ball = Vec3(ball_prediction.slices[i].physics.location)
                    future_goal_distance = future_ball.dist(our_goal_location)

                    if future_goal_distance < 800:  # Ball will be very close to goal
                        save_slice = ball_prediction.slices[i]
                        break

            if save_slice or ball_to_goal_distance < 800:
                if not isinstance(self.state, Save):
                    self.renderer.draw_string_3d(car_location, 1, 1, "Decision: SAVE!", self.renderer.red())
                    return Save(self, save_slice)

        # Priority 3: Emergency defense (ball coming toward our goal fast)
        ball_to_goal = our_goal_location - ball_location
        if ball_to_goal.length() > 0:
            ball_toward_goal = ball_velocity.dot(ball_to_goal.normalized()) > 0
        else:
            ball_toward_goal = False

        if (ball_toward_goal and ball_to_goal_distance < 3000 and
            ball_velocity.length() > 800):
            if not isinstance(self.state, Defend):
                self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Emergency Defense!", self.renderer.red())
                return Defend(self)

        # Priority 4: Low boost collection
        if my_car.boost < 20:
            if not isinstance(self.state, CollectBoost):
                self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Need Boost!", self.renderer.yellow())
                return CollectBoost(self)

        # Priority 5: Team play and defensive positioning
        # Analyze teammate positions for better decision making
        teammates = [packet.game_cars[i] for i in range(packet.num_cars)
                    if i != self.index and packet.game_cars[i].team == self.team]

        # Ball on our side of the field or we're the last defender
        is_ball_on_our_side = (ball_location.y < 0 and self.team == 0) or \
                              (ball_location.y > 0 and self.team == 1)

        # Check if we're closer to our goal than the ball (last defender)
        car_to_goal_distance = car_location.dist(our_goal_location)
        is_last_defender = car_to_goal_distance < ball_to_goal_distance

        # Team play logic - avoid double commits
        teammate_attacking = False
        if teammates:
            for teammate in teammates:
                teammate_location = Vec3(teammate.physics.location)
                teammate_to_ball = teammate_location.dist(ball_location)
                our_distance_to_ball = car_location.dist(ball_location)

                # If teammate is much closer to ball and moving toward it, let them attack
                if (teammate_to_ball < our_distance_to_ball - 500 and
                    Vec3(teammate.physics.velocity).length() > 500):
                    teammate_attacking = True
                    break

        # Defensive positioning logic
        if (is_ball_on_our_side or (is_last_defender and ball_to_goal_distance < 4000) or
            (teammate_attacking and ball_to_goal_distance < 3000)):
            if not isinstance(self.state, Defend):
                reason = "Team Defense" if teammate_attacking else "Last Defender"
                self.renderer.draw_string_3d(car_location, 1, 1, f"Decision: {reason}!", self.renderer.green())
                return Defend(self)

        # Priority 6: Opportunistic boost collection with team awareness
        if my_car.boost < 50 and car_velocity.length() < 1000:
            # Only collect boost if we're not in immediate danger and not abandoning teammates
            safe_to_collect = (ball_to_goal_distance > 2000 and
                             (not teammate_attacking or len(teammates) > 1))

            if safe_to_collect:
                if not isinstance(self.state, CollectBoost):
                    self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Boost Opportunity!", self.renderer.yellow())
                    return CollectBoost(self)

        # Priority 7: Rotation - if teammate is closer and we're both attacking, rotate back
        if (isinstance(self.state, Attack) and teammate_attacking and
            ball_to_goal_distance > 1500):
            # Rotate to a defensive position
            if not isinstance(self.state, Defend):
                self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Rotate!", self.renderer.blue())
                return Defend(self)

        # Default: Attack!
        if not isinstance(self.state, Attack):
            self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Attack!", self.renderer.white())
            return Attack(self)

        # If none of the conditions to *change* state are met, keep the current one.
        return self.state