# bot.py (Corrected Version)

from rlbot.agents.base_agent import BaseAgent, SimpleControllerState
from rlbot.utils.structures.game_data_struct import GameTickPacket

from util.boost_pad_tracker import BoostPadTracker
from util.vec import Vec3

# We import our new state classes
# Make sure you have created the states.py file!
from states import Attack, Defend, CollectBoost

class MyBot(BaseAgent):

    def __init__(self, name, team, index):
        super().__init__(name, team, index)
        self.boost_pad_tracker = BoostPadTracker()
        self.state = None  # This will hold our current state object, e.g., Attack()

    def initialize_agent(self):
        # Set up information about the boost pads now that the game is active and the info is available
        self.boost_pad_tracker.initialize_boosts(self.get_field_info())
        # The bot starts in an attack state.
        self.state = Attack(self)

    def get_output(self, packet: GameTickPacket) -> SimpleControllerState:
        """
        This function will be called by the framework many times per second. This is where you can
        see the motion of the ball, etc. and return controls to drive your car.
        """
        # Keep our boost pad info updated with which pads are currently active
        self.boost_pad_tracker.update_boost_status(packet)

        # --- THIS IS THE "BRAIN" ---
        # It decides which state we should be in and assigns it to self.state
        new_state = self.choose_state(packet)
        if type(new_state) is not type(self.state):
            self.state = new_state
        # ---------------------------------

        # Execute the logic of our current state
        return self.state.execute(packet)

    # THIS IS THE FUNCTION THAT WAS MISSING
    def choose_state(self, packet: GameTickPacket):
        my_car = packet.game_cars[self.index]
        car_location = Vec3(my_car.physics.location)
        ball_location = Vec3(packet.game_ball.physics.location)

        # Condition 1: We are low on boost.
        if my_car.boost < 25:
            # If we are not already collecting boost, switch to it.
            if not isinstance(self.state, CollectBoost):
                self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Need Boost!", self.renderer.yellow())
                return CollectBoost(self)

        # Condition 2: The ball is on our side of the field.
        # For blue team (team 0), our goal is at y = -5120. Ball on our side means ball_location.y < 0.
        # For orange team (team 1), our goal is at y = 5120. Ball on our side means ball_location.y > 0.
        is_ball_on_our_side = (ball_location.y < 0 and self.team == 0) or \
                              (ball_location.y > 0 and self.team == 1)

        if is_ball_on_our_side:
            # If we are not already defending, switch to it.
            if not isinstance(self.state, Defend):
                self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Defend!", self.renderer.green())
                return Defend(self)

        # Default Condition: If none of the above are true, attack!
        if not isinstance(self.state, Attack):
            self.renderer.draw_string_3d(car_location, 1, 1, "Decision: Attack!", self.renderer.white())
            return Attack(self)

        # If none of the conditions to *change* state are met, keep the current one.
        return self.state