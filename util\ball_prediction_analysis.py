from typing import Callable, Optional
import math

from rlbot.utils.structures.ball_prediction_struct import BallPrediction, Slice
from rlbot.utils.structures.game_data_struct import PlayerInfo

from util.vec import Vec3

# field length(5120) + ball radius(93) = 5213 however that results in false positives
GOAL_THRESHOLD = 5235

# We will jump this number of frames when looking for a moment where the ball is inside the goal.
# Big number for efficiency, but not so big that the ball could go in and then back out during that
# time span. Unit is the number of frames in the ball prediction, and the prediction is at 60 frames per second.
GOAL_SEARCH_INCREMENT = 20

# Constants for interception calculations
MAX_CAR_SPEED = 2300  # Maximum car speed in uu/s
BOOST_ACCEL = 991.666  # Boost acceleration in uu/s^2
THROTTLE_ACCEL = 1600  # Throttle acceleration in uu/s^2


def find_slice_at_time(ball_prediction: BallPrediction, game_time: float):
    """
    This will find the future position of the ball at the specified time. The returned
    Slice object will also include the ball's velocity, etc.
    """
    start_time = ball_prediction.slices[0].game_seconds
    approx_index = int((game_time - start_time) * 60)  # We know that there are 60 slices per second.
    if 0 <= approx_index < ball_prediction.num_slices:
        return ball_prediction.slices[approx_index]
    return None


def predict_future_goal(ball_prediction: BallPrediction):
    """
    Analyzes the ball prediction to see if the ball will enter one of the goals. Only works on standard arenas.
    Will return the first ball slice which appears to be inside the goal, or None if it does not enter a goal.
    """
    return find_matching_slice(ball_prediction, 0, lambda s: abs(s.physics.location.y) >= GOAL_THRESHOLD,
                               search_increment=20)


def find_matching_slice(ball_prediction: BallPrediction, start_index: int, predicate: Callable[[Slice], bool],
                        search_increment=1):
    """
    Tries to find the first slice in the ball prediction which satisfies the given predicate. For example,
    you could find the first slice below a certain height. Will skip ahead through the packet by search_increment
    for better efficiency, then backtrack to find the exact first slice.
    """
    for coarse_index in range(start_index, ball_prediction.num_slices, search_increment):
        if predicate(ball_prediction.slices[coarse_index]):
            for j in range(max(start_index, coarse_index - search_increment), coarse_index):
                ball_slice = ball_prediction.slices[j]
                if predicate(ball_slice):
                    return ball_slice
    return None


def find_best_intercept_point(ball_prediction: BallPrediction, car_location: Vec3, car_velocity: Vec3,
                             current_boost: int, current_time: float) -> Optional[Slice]:
    """
    Finds the best point to intercept the ball based on car position, velocity, and boost.
    Returns the ball slice that represents the optimal interception point.
    """
    if not ball_prediction or ball_prediction.num_slices == 0:
        return None

    best_slice = None
    best_score = float('inf')

    # Check every 5th slice for efficiency (every ~0.083 seconds)
    for i in range(0, min(ball_prediction.num_slices, 300), 5):  # Look up to 5 seconds ahead
        ball_slice = ball_prediction.slices[i]
        ball_location = Vec3(ball_slice.physics.location)
        ball_time = ball_slice.game_seconds

        # Skip if ball is too high (above 300 units) for ground intercept
        if ball_location.z > 300:
            continue

        # Calculate time needed to reach ball
        distance_to_ball = car_location.dist(ball_location)
        time_to_ball = estimate_time_to_reach(distance_to_ball, car_velocity.length(), current_boost)

        # Calculate when we would arrive vs when ball will be there
        arrival_time = current_time + time_to_ball
        time_difference = abs(arrival_time - ball_time)

        # Score based on time difference and ball height (prefer lower balls)
        score = time_difference + (ball_location.z / 1000.0)  # Height penalty

        if score < best_score and time_difference < 0.5:  # Must be within 0.5 seconds
            best_score = score
            best_slice = ball_slice

    return best_slice


def estimate_time_to_reach(distance: float, current_speed: float, boost_amount: int) -> float:
    """
    Estimates the time needed to reach a target distance given current speed and boost.
    """
    if distance <= 0:
        return 0

    # Simple physics model - assumes we can use boost efficiently
    effective_accel = THROTTLE_ACCEL
    if boost_amount > 0:
        effective_accel += BOOST_ACCEL * min(1.0, boost_amount / 33.0)  # Boost efficiency

    # Use kinematic equation: d = v0*t + 0.5*a*t^2
    # Solve for t using quadratic formula
    a = 0.5 * effective_accel
    b = current_speed
    c = -distance

    discriminant = b*b - 4*a*c
    if discriminant < 0:
        return distance / max(current_speed, 500)  # Fallback

    t1 = (-b + math.sqrt(discriminant)) / (2*a)
    t2 = (-b - math.sqrt(discriminant)) / (2*a)

    # Return the positive, smaller time
    times = [t for t in [t1, t2] if t > 0]
    return min(times) if times else distance / max(current_speed, 500)


def find_shot_opportunity(ball_prediction: BallPrediction, car_location: Vec3,
                         enemy_goal_location: Vec3, current_time: float) -> Optional[Slice]:
    """
    Finds a good opportunity to take a shot at the enemy goal.
    """
    if not ball_prediction or ball_prediction.num_slices == 0:
        return None

    best_slice = None
    best_score = 0

    for i in range(0, min(ball_prediction.num_slices, 180), 3):  # Look 3 seconds ahead
        ball_slice = ball_prediction.slices[i]
        ball_location = Vec3(ball_slice.physics.location)

        # Skip if ball is too high
        if ball_location.z > 200:
            continue

        # Calculate angle to goal from ball position
        ball_to_goal = enemy_goal_location - ball_location
        car_to_ball = ball_location - car_location

        # Skip if ball is behind us relative to goal
        if ball_to_goal.dot(car_to_ball) < 0:
            continue

        # Calculate shot angle (how direct the shot would be)
        goal_distance = ball_to_goal.length()
        if goal_distance < 500:  # Too close to goal, might be dangerous
            continue

        # Score based on goal distance and shot angle
        shot_angle = math.acos(max(-1, min(1, ball_to_goal.dot(car_to_ball) /
                                          (ball_to_goal.length() * car_to_ball.length()))))

        # Prefer shots that are more direct and closer to goal
        score = (1.0 / max(goal_distance, 500)) * (1.0 / max(shot_angle, 0.1))

        if score > best_score:
            best_score = score
            best_slice = ball_slice

    return best_slice
