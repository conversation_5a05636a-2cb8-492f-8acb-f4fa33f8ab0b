# states.py (New File)

from rlbot.agents.base_agent import SimpleControllerState
from rlbot.utils.structures.game_data_struct import GameTickPacket

# Import the utils we need
from util.drive import steer_toward_target
from util.vec import Vec3

class State:
    """
    The base class for all states. Your bot's main class will hold an instance
    of one of these, and call its execute method each tick.
    """
    def __init__(self, bot):
        self.bot = bot # A reference to the main bot class

    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        raise NotImplementedError


class Attack(State):
    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        # This is the logic from your old bot.py!
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        ball_location = Vec3(packet.game_ball.physics.location)
        
        target_location = ball_location

        # Simple lead logic (can be improved later)
        ball_prediction = self.bot.get_ball_prediction_struct()
        if ball_prediction is not None:
            future_ball_slice = ball_prediction.slices[120] # 2 seconds in the future
            target_location = Vec3(future_ball_slice.physics.location)

        # Draw some debug lines
        self.bot.renderer.draw_line_3d(car_location, target_location, self.bot.renderer.white())
        self.bot.renderer.draw_string_3d(car_location, 1, 1, "State: Attack", self.bot.renderer.white())

        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target_location)
        controls.throttle = 1.0
        controls.boost = True # Let's be aggressive for now

        return controls
        

class Defend(State):
    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        # We will defend by driving to our "back post".
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)
        
        # The coordinates of our goal.
        # The Y value is -5120 for blue team and 5120 for orange team.
        our_goal_location = Vec3(0, -5120 if self.bot.team == 0 else 5120, 0)

        # We want to drive to a point between our goal and the ball.
        # This is a simple way to find a defensive position.
        target_location = car_location.lerp(our_goal_location, 0.5) # lerp finds a point between two vectors
        
        self.bot.renderer.draw_line_3d(car_location, target_location, self.bot.renderer.green())
        self.bot.renderer.draw_string_3d(car_location, 1, 1, "State: Defend", self.bot.renderer.green())

        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target_location)
        controls.throttle = 1.0
        controls.boost = False # Conserve boost while defending

        return controls


class CollectBoost(State):
    def execute(self, packet: GameTickPacket) -> SimpleControllerState:
        # Find the closest full boost pad.
        my_car = packet.game_cars[self.bot.index]
        car_location = Vec3(my_car.physics.location)

        # Get a list of all active, full boost pads
        active_pads = [pad for pad in self.bot.boost_pad_tracker.get_full_boosts() if pad.is_active]
        
        if not active_pads:
            # No boost available, just attack
            return Attack(self.bot).execute(packet)
            
        # Find the closest one to our car
        closest_pad = min(active_pads, key=lambda pad: car_location.dist(pad.location))
        target_location = closest_pad.location

        self.bot.renderer.draw_line_3d(car_location, target_location, self.bot.renderer.yellow())
        self.bot.renderer.draw_string_3d(car_location, 1, 1, "State: Collect Boost", self.bot.renderer.yellow())
        
        controls = SimpleControllerState()
        controls.steer = steer_toward_target(my_car, target_location)
        controls.throttle = 1.0
        controls.boost = True # Gotta get there fast

        return controls