# Game Awareness Utilities for Advanced Bot Behavior

from typing import List, Optional, Tuple
import math

from rlbot.utils.structures.game_data_struct import GameTickPacket, PlayerInfo
from rlbot.utils.structures.ball_prediction_struct import BallPrediction

from util.vec import Vec3
from util.orientation import Orientation


class OpponentTracker:
    """Tracks opponent positions and predicts their actions"""
    
    def __init__(self):
        self.opponent_positions = {}
        self.opponent_velocities = {}
        self.last_update_time = 0
    
    def update(self, packet: GameTickPacket, bot_team: int):
        """Update opponent tracking data"""
        current_time = packet.game_info.seconds_elapsed
        
        for i in range(packet.num_cars):
            car = packet.game_cars[i]
            if car.team != bot_team:  # This is an opponent
                self.opponent_positions[i] = Vec3(car.physics.location)
                self.opponent_velocities[i] = Vec3(car.physics.velocity)
        
        self.last_update_time = current_time
    
    def get_closest_opponent_to_ball(self, ball_location: Vec3) -> Optional[Tuple[int, float]]:
        """Returns (opponent_index, distance) of closest opponent to ball"""
        closest_opponent = None
        closest_distance = float('inf')
        
        for opponent_id, position in self.opponent_positions.items():
            distance = position.dist(ball_location)
            if distance < closest_distance:
                closest_distance = distance
                closest_opponent = opponent_id
        
        return (closest_opponent, closest_distance) if closest_opponent is not None else None
    
    def will_opponent_reach_ball_first(self, ball_location: Vec3, bot_location: Vec3, 
                                     bot_velocity: Vec3, time_horizon: float = 3.0) -> bool:
        """Predicts if any opponent will reach the ball before the bot"""
        bot_distance = bot_location.dist(ball_location)
        bot_speed = bot_velocity.length()
        bot_time_estimate = bot_distance / max(bot_speed, 500)  # Minimum assumed speed
        
        for opponent_id, position in self.opponent_positions.items():
            opponent_distance = position.dist(ball_location)
            opponent_velocity = self.opponent_velocities.get(opponent_id, Vec3(0, 0, 0))
            opponent_speed = opponent_velocity.length()
            
            # Estimate opponent time to ball
            opponent_time_estimate = opponent_distance / max(opponent_speed, 500)
            
            # If opponent will reach significantly earlier, return True
            if opponent_time_estimate < bot_time_estimate - 0.5:
                return True
        
        return False


class BallTrajectoryAnalyzer:
    """Analyzes ball trajectory for smart positioning"""
    
    @staticmethod
    def find_ball_landing_point(ball_prediction: BallPrediction, 
                               max_time_ahead: float = 5.0) -> Optional[Vec3]:
        """Find where the ball will land (z < 100)"""
        if not ball_prediction or ball_prediction.num_slices == 0:
            return None
        
        max_slices = min(int(max_time_ahead * 60), ball_prediction.num_slices)
        
        for i in range(0, max_slices, 5):  # Check every 5th slice for efficiency
            ball_slice = ball_prediction.slices[i]
            ball_location = Vec3(ball_slice.physics.location)
            
            # Check if ball is close to ground
            if ball_location.z < 100:
                return ball_location
        
        return None
    
    @staticmethod
    def is_ball_coming_down(ball_prediction: BallPrediction, 
                           near_location: Vec3, radius: float = 500) -> bool:
        """Check if ball is coming down near a specific location"""
        if not ball_prediction or ball_prediction.num_slices == 0:
            return False
        
        # Check next 3 seconds
        max_slices = min(180, ball_prediction.num_slices)
        
        for i in range(0, max_slices, 10):
            ball_slice = ball_prediction.slices[i]
            ball_location = Vec3(ball_slice.physics.location)
            ball_velocity = Vec3(ball_slice.physics.velocity)
            
            # Ball is coming down (negative z velocity) and near our location
            if (ball_velocity.z < -200 and 
                ball_location.dist(near_location) < radius and
                ball_location.z > 150):  # Still airborne but coming down
                return True
        
        return False
    
    @staticmethod
    def predict_ball_bounce(ball_prediction: BallPrediction, 
                           time_ahead: float = 2.0) -> Optional[Vec3]:
        """Predict where ball will bounce next"""
        if not ball_prediction or ball_prediction.num_slices == 0:
            return None
        
        max_slices = min(int(time_ahead * 60), ball_prediction.num_slices)
        
        for i in range(1, max_slices):
            current_slice = ball_prediction.slices[i]
            prev_slice = ball_prediction.slices[i-1]
            
            current_z = current_slice.physics.location.z
            prev_z = prev_slice.physics.location.z
            current_vel_z = current_slice.physics.velocity.z
            prev_vel_z = prev_slice.physics.velocity.z
            
            # Detect bounce: ball was going down, now going up, and near ground
            if (prev_vel_z < -100 and current_vel_z > 50 and 
                current_z < 200 and prev_z > current_z):
                return Vec3(current_slice.physics.location)
        
        return None


class GameSituationAnalyzer:
    """Analyzes overall game situation for decision making"""
    
    @staticmethod
    def should_be_patient(bot_location: Vec3, ball_location: Vec3, 
                         ball_prediction: BallPrediction, 
                         opponent_tracker: OpponentTracker) -> bool:
        """Determine if bot should wait instead of chasing"""
        
        # If ball is coming down near us, be patient
        if BallTrajectoryAnalyzer.is_ball_coming_down(ball_prediction, bot_location, 800):
            return True
        
        # If opponent will reach ball first, be patient and position defensively
        bot_velocity = Vec3(0, 0, 0)  # Simplified for this check
        if opponent_tracker.will_opponent_reach_ball_first(ball_location, bot_location, bot_velocity):
            return True
        
        return False
    
    @staticmethod
    def calculate_defensive_position(bot_location: Vec3, ball_location: Vec3, 
                                   goal_location: Vec3, shadow_distance: float = 1500) -> Vec3:
        """Calculate optimal defensive shadow position"""
        ball_to_goal = goal_location - ball_location
        
        if ball_to_goal.length() == 0:
            return goal_location
        
        # Position between ball and goal at shadow distance
        direction = ball_to_goal.normalized()
        shadow_position = goal_location + direction * shadow_distance
        
        # Clamp to reasonable field boundaries
        shadow_position.x = max(-4000, min(4000, shadow_position.x))
        shadow_position.y = max(-5000, min(5000, shadow_position.y))
        
        return shadow_position
    
    @staticmethod
    def should_attempt_aerial(bot_location: Vec3, ball_location: Vec3, 
                            bot_boost: int, ball_prediction: BallPrediction) -> Optional[Vec3]:
        """Determine if aerial attempt is worthwhile and return target"""
        if bot_boost < 30:  # Need minimum boost for aerial
            return None
        
        if not ball_prediction or ball_prediction.num_slices == 0:
            return None
        
        # Look for aerial opportunities in next 2-3 seconds
        for i in range(60, min(180, ball_prediction.num_slices), 10):
            ball_slice = ball_prediction.slices[i]
            future_ball = Vec3(ball_slice.physics.location)
            
            # Check if ball is at good aerial height and reachable
            if (300 < future_ball.z < 1000 and 
                bot_location.dist(future_ball) < 1500):
                
                # Estimate if we can reach it
                time_to_ball = ball_slice.game_seconds - ball_prediction.slices[0].game_seconds
                distance = bot_location.dist(future_ball)
                
                # Rough aerial feasibility check
                if 0.8 < time_to_ball < 2.5 and distance / time_to_ball < 1800:
                    return future_ball
        
        return None
