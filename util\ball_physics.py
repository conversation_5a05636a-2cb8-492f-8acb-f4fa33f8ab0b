# Advanced Ball Physics and Contact Mechanics

import math
from typing import <PERSON><PERSON>, Tuple
from util.vec import Vec3
from util.orientation import Orientation


class BallContactPhysics:
    """Handles sophisticated ball contact physics and manipulation"""
    
    # Ball and car physical constants
    BALL_RADIUS = 93.15  # Rocket League ball radius in UU
    CAR_LENGTH = 118.0   # Approximate car length
    CAR_WIDTH = 84.2     # Approximate car width
    CAR_HEIGHT = 36.16   # Approximate car height
    
    @staticmethod
    def calculate_ball_contact_point(car_location: Vec3, car_orientation: Orientation, 
                                   ball_location: Vec3, desired_ball_direction: Vec3) -> Vec3:
        """
        Calculate where on the ball to make contact to send it in the desired direction.
        Uses real physics: hitting left side sends ball right, etc.
        """
        # Vector from ball center to desired direction (opposite of where we want ball to go)
        ball_to_desired = desired_ball_direction.normalized()
        
        # The contact point is on the opposite side of where we want the ball to go
        # If we want ball to go right, we hit the left side
        contact_offset = ball_to_desired * -BallContactPhysics.BALL_RADIUS
        contact_point = ball_location + contact_offset
        
        return contact_point
    
    @staticmethod
    def calculate_approach_vector(car_location: Vec3, ball_location: Vec3, 
                                target_direction: Vec3, approach_distance: float = 150) -> Vec3:
        """
        Calculate where the car should position itself to hit the ball in the target direction.
        """
        # Normalize target direction
        target_dir = target_direction.normalized()
        
        # Position car opposite to target direction, at approach distance from ball
        approach_position = ball_location - (target_dir * approach_distance)
        
        return approach_position
    
    @staticmethod
    def predict_ball_trajectory_after_hit(ball_location: Vec3, ball_velocity: Vec3,
                                        car_velocity: Vec3, contact_normal: Vec3,
                                        hit_strength: float = 1.0) -> Vec3:
        """
        Predict ball velocity after being hit by the car.
        Uses simplified momentum transfer physics.
        """
        # Car's velocity component in the direction of contact
        car_impact_velocity = car_velocity.dot(contact_normal)
        
        # Ball's velocity component in the direction of contact
        ball_impact_velocity = ball_velocity.dot(contact_normal)
        
        # Momentum transfer (simplified)
        velocity_transfer = (car_impact_velocity - ball_impact_velocity) * hit_strength
        
        # New ball velocity
        new_ball_velocity = ball_velocity + (contact_normal * velocity_transfer)
        
        return new_ball_velocity
    
    @staticmethod
    def calculate_optimal_hit_angle(ball_location: Vec3, target_location: Vec3,
                                  car_location: Vec3) -> Tuple[Vec3, float]:
        """
        Calculate the optimal angle to hit the ball toward a target.
        Returns (contact_point, hit_angle)
        """
        # Direction from ball to target
        ball_to_target = (target_location - ball_location).normalized()
        
        # Calculate contact point (opposite side of target direction)
        contact_point = ball_location - (ball_to_target * BallContactPhysics.BALL_RADIUS)
        
        # Calculate angle between car approach and optimal hit direction
        car_to_contact = (contact_point - car_location).normalized()
        hit_angle = math.acos(max(-1, min(1, car_to_contact.dot(ball_to_target))))
        
        return contact_point, hit_angle
    
    @staticmethod
    def should_flip_into_ball(car_location: Vec3, ball_location: Vec3,
                            car_velocity: Vec3, target_direction: Vec3) -> bool:
        """
        Determine if the car should flip into the ball for more power.
        """
        distance_to_ball = car_location.dist(ball_location)
        car_speed = car_velocity.length()
        
        # Flip if we're close, moving fast, and aligned with target
        if distance_to_ball < 200 and car_speed > 800:
            car_to_ball = (ball_location - car_location).normalized()
            alignment = car_to_ball.dot(target_direction.normalized())
            return alignment > 0.7  # Good alignment
        
        return False
    
    @staticmethod
    def calculate_dribble_position(ball_location: Vec3, ball_velocity: Vec3,
                                 car_location: Vec3, target_direction: Vec3) -> Vec3:
        """
        Calculate position for dribbling the ball.
        """
        # Position slightly behind and below the ball
        dribble_offset = target_direction.normalized() * -120  # Behind ball
        dribble_position = ball_location + dribble_offset
        dribble_position.z = max(0, dribble_position.z - 50)  # Lower than ball
        
        return dribble_position


class ShotCalculator:
    """Calculates sophisticated shot opportunities and angles"""
    
    @staticmethod
    def find_best_shot_angle(ball_location: Vec3, goal_location: Vec3,
                           goal_width: float = 1400) -> Tuple[Vec3, float]:
        """
        Find the best angle to shoot at the goal.
        Returns (target_point, shot_quality_score)
        """
        # Calculate goal posts
        goal_left = goal_location + Vec3(-goal_width/2, 0, 0)
        goal_right = goal_location + Vec3(goal_width/2, 0, 0)
        goal_center = goal_location
        
        # Possible target points
        targets = [
            (goal_left, "left_post"),
            (goal_right, "right_post"),
            (goal_center, "center"),
            (goal_left + Vec3(200, 0, 0), "left_side"),
            (goal_right + Vec3(-200, 0, 0), "right_side")
        ]
        
        best_target = goal_center
        best_score = 0
        
        for target, target_type in targets:
            # Calculate shot angle and distance
            shot_vector = target - ball_location
            shot_distance = shot_vector.length()
            
            # Score based on distance and angle
            distance_score = max(0, 1.0 - (shot_distance / 3000))  # Closer is better
            
            # Prefer corners for better angles
            angle_score = 0.8 if target_type in ["left_side", "right_side"] else 0.6
            
            total_score = distance_score * angle_score
            
            if total_score > best_score:
                best_score = total_score
                best_target = target
        
        return best_target, best_score
    
    @staticmethod
    def calculate_shot_power(distance_to_goal: float, ball_height: float) -> float:
        """
        Calculate how much power to put into a shot based on distance and height.
        """
        # Base power from distance
        distance_power = min(1.0, distance_to_goal / 2000)
        
        # Height adjustment
        height_power = min(1.0, ball_height / 200)
        
        # Combine factors
        total_power = max(0.3, distance_power + height_power * 0.3)
        
        return min(1.0, total_power)


class BallManipulation:
    """High-level ball manipulation strategies"""
    
    @staticmethod
    def plan_ball_touch(ball_location: Vec3, ball_velocity: Vec3,
                       car_location: Vec3, car_velocity: Vec3,
                       desired_outcome: str, target_location: Vec3 = None) -> dict:
        """
        Plan a sophisticated ball touch based on desired outcome.
        
        desired_outcome options:
        - "shot": Shoot toward target
        - "pass": Pass to target location
        - "clear": Clear ball away from danger
        - "dribble": Set up for dribbling
        - "control": Slow down ball for control
        """
        
        plan = {
            "approach_position": car_location,
            "contact_point": ball_location,
            "should_boost": False,
            "should_flip": False,
            "target_direction": Vec3(1, 0, 0),
            "hit_strength": 0.5,
            "strategy": desired_outcome
        }
        
        if desired_outcome == "shot" and target_location:
            # Calculate shot
            target_point, shot_quality = ShotCalculator.find_best_shot_angle(
                ball_location, target_location)
            
            plan["target_direction"] = (target_point - ball_location).normalized()
            plan["approach_position"] = BallContactPhysics.calculate_approach_vector(
                car_location, ball_location, plan["target_direction"], 180)
            plan["contact_point"] = BallContactPhysics.calculate_ball_contact_point(
                car_location, None, ball_location, plan["target_direction"])
            plan["should_boost"] = shot_quality > 0.6
            plan["should_flip"] = BallContactPhysics.should_flip_into_ball(
                car_location, ball_location, car_velocity, plan["target_direction"])
            plan["hit_strength"] = ShotCalculator.calculate_shot_power(
                ball_location.dist(target_location), ball_location.z)
        
        elif desired_outcome == "clear":
            # Clear away from our goal
            our_goal = Vec3(0, -5120 if target_location and target_location.y > 0 else 5120, 0)
            clear_direction = (ball_location - our_goal).normalized()
            
            plan["target_direction"] = clear_direction
            plan["approach_position"] = BallContactPhysics.calculate_approach_vector(
                car_location, ball_location, clear_direction, 150)
            plan["should_boost"] = True
            plan["hit_strength"] = 0.8
        
        elif desired_outcome == "dribble":
            # Set up for dribbling
            dribble_direction = (target_location - ball_location).normalized() if target_location else Vec3(1, 0, 0)
            plan["approach_position"] = BallContactPhysics.calculate_dribble_position(
                ball_location, ball_velocity, car_location, dribble_direction)
            plan["target_direction"] = dribble_direction
            plan["should_boost"] = False
            plan["hit_strength"] = 0.3
        
        elif desired_outcome == "control":
            # Slow down the ball
            ball_direction = ball_velocity.normalized() if ball_velocity.length() > 0 else Vec3(1, 0, 0)
            plan["target_direction"] = ball_direction * -0.5  # Opposite to ball movement
            plan["approach_position"] = ball_location + ball_direction * 100
            plan["hit_strength"] = 0.2
        
        return plan
