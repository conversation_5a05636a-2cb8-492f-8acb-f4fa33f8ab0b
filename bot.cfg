[Locations]
# Path to loadout config. Can use relative path from here.
looks_config = ./appearance.cfg

# Path to python file. Can use relative path from here.
python_file = ./bot.py

# Name of the bot in-game
name = HiddenGemV.0.1

# The maximum number of ticks per second that your bot wishes to receive.
maximum_tick_rate_preference = 120

[Details]
# These values are optional but useful metadata for helper programs
# Name of the bot's creator/developer
developer = The RLBot community

# Short description of the bot
description = This is a multi-line description
    of the official python example bot

# Fun fact about the bot
fun_fact =

# Link to github repository
github = https://github.com/RLBot/RLBotPythonExample

# Programming language
language = python
